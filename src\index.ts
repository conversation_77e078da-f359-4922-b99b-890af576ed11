import express, { Request, Response } from "express";
import axios from "axios";
import { Client, LocalAuth, Message, MessageMedia } from "whatsapp-web.js";
import qrcode from "qrcode-terminal";
import { GoogleGenerativeAI, ChatSession } from "@google/generative-ai";
import 'dotenv/config';

const genAI = new GoogleGenerativeAI(process.env.API_KEY!);

const app = express();
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

const port = 5000;

async function mediaToGenerativePart(media: MessageMedia) {
  return {
    inlineData: { data: media.data, mimeType: media.mimetype },
  };
}

const whatsappClient = new Client({
  authStrategy: new LocalAuth(),
  puppeteer: {
    args: ['--no-sandbox', '--disable-setuid-sandbox'], 
  },
});

whatsappClient.on("qr", (qr: string) => {
  qrcode.generate(qr, { small: true });
  console.log("Kode QR diterima, pindai dengan ponsel Anda.");
});

whatsappClient.on("ready", () => {
  console.log("Klien WhatsApp Web sudah siap!");
});

whatsappClient.on("message", async (msg: Message) => {
  const senderNumber: string = msg.from;
  const message: string = msg.body;

  console.log(`Pesan diterima dari ${senderNumber}: ${message}`);

  // Cek apakah pesan dimulai dengan "/"
  if (!message.startsWith("/")) {
    await sendWhatsAppMessage("Halo! Untuk memulai percakapan dengan bot, silakan awali pesan Anda dengan tanda '/' (slash).\n\nContoh: /halo atau /tanya sesuatu\n/selesai - untuk menghapus memori percakapan", senderNumber);
    return;
  }

  // Hapus "/" dari awal pesan
  const cleanMessage = message.substring(1);

  // Cek apakah pesan adalah perintah "/selesai"
  if (cleanMessage.toLowerCase() === "selesai") {
    chat = null; // Reset chat session
    await sendWhatsAppMessage("✅ Memori percakapan telah dihapus. Anda dapat memulai percakapan baru sekarang.", senderNumber);
    return;
  }

  let mediaPart = null;

  if (msg.hasMedia) {
    const media = await msg.downloadMedia();
    mediaPart = await mediaToGenerativePart(media);
  }

  await run(cleanMessage, senderNumber, mediaPart);
});

whatsappClient.initialize();

let chat: ChatSession | null = null;

async function run(message: string, senderNumber: string, mediaPart?: any): Promise<void> {
  try {
    const model = genAI.getGenerativeModel({ model: "gemini-2.5-pro-preview-06-05" });

    if (!chat) {
      chat = model.startChat({
        generationConfig: {
          maxOutputTokens: 65536,
        },
        history: []
      });
    }
    let prompt: any[] = [];

    // Tambahkan instruksi bahasa Indonesia, data-driven, dan link referensi ke setiap pesan
    const indonesianInstruction = `Jawab dalam bahasa Indonesia dengan data dan fakta yang akurat, serta WAJIB sertakan link referensi di akhir jawaban: ${message}`;
    prompt.push(indonesianInstruction);

    if (mediaPart) {
      prompt.push(mediaPart);
    }

    const result = await chat.sendMessage(prompt);
    const response = await result.response;
    const text: string = response.text();


    if (text) {
      console.log("Teks yang Dihasilkan:", text);
      await sendWhatsAppMessage(text, senderNumber);
    } else {
      console.error("Masalah ini terkait dengan Keterbatasan Model dan Batas Tingkat API");
    }

  } catch (error) {
    console.error("Kesalahan dalam fungsi run:", error);
    await sendWhatsAppMessage("Ups, terjadi kesalahan. Silakan coba lagi nanti.", senderNumber);
  }
}

async function sendWhatsAppMessage(text: string, toNumber: string): Promise<void> {
  try {
    await whatsappClient.sendMessage(toNumber, text);
  } catch (err) {
    console.error("Gagal mengirim pesan WhatsApp:");
    console.error("Detail kesalahan:", err);
  }
}

app.listen(port, () => console.log(`Aplikasi Express berjalan di port ${port}!`));
