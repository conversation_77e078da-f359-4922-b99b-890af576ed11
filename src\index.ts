import express, { Request, Response } from "express";
import axios from "axios";
import { Client, LocalAuth, Message, MessageMedia } from "whatsapp-web.js";
import qrcode from "qrcode-terminal";
import { GoogleGenerativeAI, ChatSession } from "@google/generative-ai";
import 'dotenv/config';

const genAI = new GoogleGenerativeAI(process.env.API_KEY!);

const app = express();
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

const port = 5000;

async function mediaToGenerativePart(media: MessageMedia) {
  return {
    inlineData: { data: media.data, mimeType: media.mimetype },
  };
}

const whatsappClient = new Client({
  authStrategy: new LocalAuth(),
  puppeteer: {
    args: ['--no-sandbox', '--disable-setuid-sandbox'], 
  },
});

whatsappClient.on("qr", (qr: string) => {
  qrcode.generate(qr, { small: true });
  console.log("Kode QR diterima, pindai dengan ponsel Anda.");
});

whatsappClient.on("ready", () => {
  console.log("Klien WhatsApp Web sudah siap!");
  console.log("🕐 Auto-clear memory diaktifkan: setiap 4 jam");
  console.log(`⏰ Waktu mulai: ${lastMemoryClearTime.toLocaleString('id-ID', { timeZone: 'Asia/Jakarta' })}`);
});

whatsappClient.on("message", async (msg: Message) => {
  const senderNumber: string = msg.from;
  const message: string = msg.body;

  console.log(`Pesan diterima dari ${senderNumber}: ${message}`);

  // Cek apakah pesan dimulai dengan "/"
  if (!message.startsWith("/")) {
    await sendWhatsAppMessage("🤖 **Halo! Selamat datang di WhatsApp Bot AI**\n\n" +
      "Untuk memulai percakapan dengan bot, silakan awali pesan Anda dengan tanda '/' (slash).\n\n" +
      "**Contoh penggunaan:**\n" +
      "• /halo\n" +
      "• /tanya tentang cuaca\n" +
      "• /jam berapa sekarang?\n\n" +
      "**Perintah khusus:**\n" +
      "• /selesai - Hapus memori percakapan\n" +
      "• /status - Lihat status bot\n\n" +
      "ℹ️ *Memori percakapan akan dihapus otomatis setiap 4 jam*", senderNumber);
    return;
  }

  // Hapus "/" dari awal pesan
  const cleanMessage = message.substring(1);

  // Cek apakah pesan adalah perintah "/selesai"
  if (cleanMessage.toLowerCase() === "selesai") {
    clearMemory(); // Gunakan fungsi clearMemory yang baru
    await sendWhatsAppMessage("✅ Memori percakapan telah dihapus secara manual. Anda dapat memulai percakapan baru sekarang.", senderNumber);
    return;
  }

  // Cek apakah pesan adalah perintah "/status"
  if (cleanMessage.toLowerCase() === "status") {
    const now = new Date();
    const timeSinceLastClear = now.getTime() - lastMemoryClearTime.getTime();
    const timeUntilNextClear = MEMORY_CLEAR_INTERVAL - timeSinceLastClear;

    const hoursUntilClear = Math.floor(timeUntilNextClear / (1000 * 60 * 60));
    const minutesUntilClear = Math.floor((timeUntilNextClear % (1000 * 60 * 60)) / (1000 * 60));

    const statusMessage = `📊 **Status Bot WhatsApp AI**\n\n` +
      `🤖 Status: Aktif dan siap melayani\n` +
      `💾 Memori: ${chat ? 'Tersimpan' : 'Kosong'}\n` +
      `🕐 Terakhir clear: ${lastMemoryClearTime.toLocaleString('id-ID', { timeZone: 'Asia/Jakarta' })}\n` +
      `⏰ Clear berikutnya: ${hoursUntilClear}j ${minutesUntilClear}m lagi\n` +
      `🔄 Auto-clear: Setiap 4 jam\n\n` +
      `**Perintah tersedia:**\n` +
      `• /selesai - Hapus memori manual\n` +
      `• /status - Lihat status ini\n` +
      `• /[pertanyaan] - Tanya AI`;

    await sendWhatsAppMessage(statusMessage, senderNumber);
    return;
  }

  let mediaPart = null;

  if (msg.hasMedia) {
    const media = await msg.downloadMedia();
    mediaPart = await mediaToGenerativePart(media);
  }

  await run(cleanMessage, senderNumber, mediaPart);
});

whatsappClient.initialize();

let chat: ChatSession | null = null;
let lastMemoryClearTime: Date = new Date();

// Fungsi untuk menghapus memori
function clearMemory(): void {
  chat = null;
  lastMemoryClearTime = new Date();
  console.log(`🧹 Memori percakapan dihapus otomatis pada: ${lastMemoryClearTime.toLocaleString('id-ID', { timeZone: 'Asia/Jakarta' })}`);
}

// Timer untuk menghapus memori setiap 4 jam (4 * 60 * 60 * 1000 ms)
const MEMORY_CLEAR_INTERVAL = 4 * 60 * 60 * 1000; // 4 jam dalam milliseconds

// Set interval untuk auto-clear memory
setInterval(() => {
  clearMemory();
}, MEMORY_CLEAR_INTERVAL);

// Fungsi untuk cek apakah perlu clear memory (backup check)
function checkMemoryClearNeeded(): boolean {
  const now = new Date();
  const timeDiff = now.getTime() - lastMemoryClearTime.getTime();
  return timeDiff >= MEMORY_CLEAR_INTERVAL;
}

async function run(message: string, senderNumber: string, mediaPart?: any): Promise<void> {
  try {
    // Cek apakah perlu clear memory (backup check)
    if (checkMemoryClearNeeded()) {
      clearMemory();
    }

    const model = genAI.getGenerativeModel({ model: "gemini-2.5-pro-preview-06-05" });

    if (!chat) {
      chat = model.startChat({
        generationConfig: {
          maxOutputTokens: 65536,
        },
        history: []
      });
    }
    let prompt: any[] = [];

    // Tambahkan instruksi bahasa Indonesia, data-driven, dan link referensi ke setiap pesan
    const indonesianInstruction = `Jawab dalam bahasa Indonesia dengan data dan fakta yang akurat, serta WAJIB sertakan link referensi di akhir jawaban: ${message}`;
    prompt.push(indonesianInstruction);

    if (mediaPart) {
      prompt.push(mediaPart);
    }

    const result = await chat.sendMessage(prompt);
    const response = await result.response;
    const text: string = response.text();


    if (text) {
      console.log("Teks yang Dihasilkan:", text);
      await sendWhatsAppMessage(text, senderNumber);
    } else {
      console.error("Masalah ini terkait dengan Keterbatasan Model dan Batas Tingkat API");
    }

  } catch (error) {
    console.error("Kesalahan dalam fungsi run:", error);
    await sendWhatsAppMessage("Ups, terjadi kesalahan. Silakan coba lagi nanti.", senderNumber);
  }
}

async function sendWhatsAppMessage(text: string, toNumber: string): Promise<void> {
  try {
    await whatsappClient.sendMessage(toNumber, text);
  } catch (err) {
    console.error("Gagal mengirim pesan WhatsApp:");
    console.error("Detail kesalahan:", err);
  }
}

app.listen(port, () => console.log(`Aplikasi Express berjalan di port ${port}!`));
